/**
 * AIDesignAssistantPanel.tsx
 * 
 * AI设计助手面板组件
 */

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  Card,
  Button,
  List,
  Badge,
  Space,
  Tabs,
  Progress,
  Tooltip,
  Alert,
  Spin,
  Empty,
  Tag,
  Collapse,
  Switch,
  Divider,
  Typography,
  Rate,
  message
} from 'antd';
import {
  BulbOutlined,
  RobotOutlined,
  CheckOutlined,
  CloseOutlined,
  EyeOutlined,
  ThunderboltOutlined,
  SettingOutlined,
  ReloadOutlined,
  StarOutlined,
  WarningOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { aiDesignAssistant, AISuggestion, AISuggestionType, AISuggestionPriority } from '../../services/AIDesignAssistant';
import { UIElementData } from '../ui/UIVisualEditor';
import './AIDesignAssistantPanel.scss';

const { TabPane } = Tabs;
const { Panel } = Collapse;
const { Text, Title } = Typography;

/**
 * AI设计助手面板属性
 */
export interface AIDesignAssistantPanelProps {
  /** 当前UI元素 */
  elements: UIElementData[];
  /** 选中的元素 */
  selectedElements: UIElementData[];
  /** 元素变化回调 */
  onElementsChange?: (elements: UIElementData[]) => void;
  /** 是否显示 */
  visible?: boolean;
  /** 样式类名 */
  className?: string;
}

/**
 * 建议类型图标映射
 */
const SUGGESTION_TYPE_ICONS = {
  [AISuggestionType.LAYOUT_OPTIMIZATION]: <ThunderboltOutlined />,
  [AISuggestionType.COLOR_HARMONY]: <BulbOutlined />,
  [AISuggestionType.SPACING_CONSISTENCY]: <SettingOutlined />,
  [AISuggestionType.ACCESSIBILITY]: <EyeOutlined />,
  [AISuggestionType.RESPONSIVE_DESIGN]: <SettingOutlined />,
  [AISuggestionType.COMPONENT_GROUPING]: <SettingOutlined />,
  [AISuggestionType.DESIGN_PATTERN]: <StarOutlined />,
  [AISuggestionType.PERFORMANCE]: <ThunderboltOutlined />
};

/**
 * 优先级颜色映射
 */
const PRIORITY_COLORS = {
  [AISuggestionPriority.CRITICAL]: '#ff4d4f',
  [AISuggestionPriority.HIGH]: '#fa8c16',
  [AISuggestionPriority.MEDIUM]: '#1890ff',
  [AISuggestionPriority.LOW]: '#52c41a'
};

/**
 * AI设计助手面板组件
 */
export const AIDesignAssistantPanel: React.FC<AIDesignAssistantPanelProps> = ({
  elements,
  selectedElements,
  onElementsChange,
  visible = true,
  className
}) => {
  const [suggestions, setSuggestions] = useState<AISuggestion[]>([]);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('suggestions');
  const [autoAnalyze, setAutoAnalyze] = useState(true);
  const [appliedSuggestions, setAppliedSuggestions] = useState<Set<string>>(new Set());
  const [ignoredSuggestions, setIgnoredSuggestions] = useState<Set<string>>(new Set());

  // 分析设计
  const analyzeDesign = useCallback(async () => {
    if (elements.length === 0) {
      setSuggestions([]);
      return;
    }

    setLoading(true);
    try {
      const newSuggestions = await aiDesignAssistant.analyzeDesign(elements);
      setSuggestions(newSuggestions);
    } catch (error) {
      message.error('AI分析失败，请稍后重试');
      console.error('AI分析错误:', error);
    } finally {
      setLoading(false);
    }
  }, [elements]);

  // 自动分析
  useEffect(() => {
    if (autoAnalyze && elements.length > 0) {
      const timer = setTimeout(() => {
        analyzeDesign();
      }, 1000); // 延迟1秒避免频繁分析

      return () => clearTimeout(timer);
    }
  }, [elements, autoAnalyze, analyzeDesign]);

  // 应用建议
  const applySuggestion = useCallback(async (suggestion: AISuggestion) => {
    try {
      // 这里应该实现具体的建议应用逻辑
      // 根据suggestion.actions来修改elements
      
      setAppliedSuggestions(prev => new Set([...prev, suggestion.id]));
      message.success(`已应用建议: ${suggestion.title}`);
      
      // 重新分析
      if (autoAnalyze) {
        setTimeout(analyzeDesign, 500);
      }
    } catch (error) {
      message.error('应用建议失败');
    }
  }, [autoAnalyze, analyzeDesign]);

  // 忽略建议
  const ignoreSuggestion = useCallback((suggestion: AISuggestion) => {
    setIgnoredSuggestions(prev => new Set([...prev, suggestion.id]));
    message.info(`已忽略建议: ${suggestion.title}`);
  }, []);

  // 预览建议
  const previewSuggestion = useCallback((suggestion: AISuggestion) => {
    // 实现建议预览逻辑
    message.info('预览功能开发中...');
  }, []);

  // 过滤建议
  const filteredSuggestions = useMemo(() => {
    return suggestions.filter(suggestion => 
      !appliedSuggestions.has(suggestion.id) && 
      !ignoredSuggestions.has(suggestion.id)
    );
  }, [suggestions, appliedSuggestions, ignoredSuggestions]);

  // 按优先级分组建议
  const suggestionsByPriority = useMemo(() => {
    const groups = {
      [AISuggestionPriority.CRITICAL]: [],
      [AISuggestionPriority.HIGH]: [],
      [AISuggestionPriority.MEDIUM]: [],
      [AISuggestionPriority.LOW]: []
    };

    filteredSuggestions.forEach(suggestion => {
      groups[suggestion.priority].push(suggestion);
    });

    return groups;
  }, [filteredSuggestions]);

  // 计算总体评分
  const overallScore = useMemo(() => {
    if (suggestions.length === 0) return 100;
    
    const criticalCount = suggestionsByPriority[AISuggestionPriority.CRITICAL].length;
    const highCount = suggestionsByPriority[AISuggestionPriority.HIGH].length;
    const mediumCount = suggestionsByPriority[AISuggestionPriority.MEDIUM].length;
    const lowCount = suggestionsByPriority[AISuggestionPriority.LOW].length;
    
    const penalty = criticalCount * 30 + highCount * 20 + mediumCount * 10 + lowCount * 5;
    return Math.max(0, 100 - penalty);
  }, [suggestionsByPriority]);

  // 渲染建议项
  const renderSuggestionItem = useCallback((suggestion: AISuggestion) => (
    <List.Item
      key={suggestion.id}
      className="suggestion-item"
      actions={[
        <Tooltip title="预览效果">
          <Button
            type="text"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => previewSuggestion(suggestion)}
          />
        </Tooltip>,
        <Tooltip title="应用建议">
          <Button
            type="primary"
            size="small"
            icon={<CheckOutlined />}
            onClick={() => applySuggestion(suggestion)}
          >
            应用
          </Button>
        </Tooltip>,
        <Tooltip title="忽略建议">
          <Button
            type="text"
            size="small"
            icon={<CloseOutlined />}
            onClick={() => ignoreSuggestion(suggestion)}
          />
        </Tooltip>
      ]}
    >
      <List.Item.Meta
        avatar={
          <Badge
            dot
            color={PRIORITY_COLORS[suggestion.priority]}
          >
            {SUGGESTION_TYPE_ICONS[suggestion.type]}
          </Badge>
        }
        title={
          <Space>
            <span>{suggestion.title}</span>
            <Rate
              disabled
              count={5}
              value={suggestion.confidence * 5}
              style={{ fontSize: 12 }}
            />
          </Space>
        }
        description={
          <div>
            <p>{suggestion.description}</p>
            <Space size="small">
              <Tag color={PRIORITY_COLORS[suggestion.priority]}>
                {suggestion.priority}
              </Tag>
              <Text type="secondary">
                置信度: {(suggestion.confidence * 100).toFixed(0)}%
              </Text>
              <Text type="secondary">
                影响: {suggestion.impact}
              </Text>
            </Space>
          </div>
        }
      />
    </List.Item>
  ), [applySuggestion, ignoreSuggestion, previewSuggestion]);

  // 渲染建议列表
  const renderSuggestionsList = useCallback(() => {
    if (loading) {
      return (
        <div className="loading-container">
          <Spin size="large" />
          <p>AI正在分析您的设计...</p>
        </div>
      );
    }

    if (filteredSuggestions.length === 0) {
      return (
        <Empty
          image={<RobotOutlined style={{ fontSize: 64, color: '#d9d9d9' }} />}
          description={
            elements.length === 0 
              ? "请添加一些UI元素开始设计" 
              : "太棒了！您的设计没有发现问题"
          }
        />
      );
    }

    return (
      <div className="suggestions-list">
        {Object.entries(suggestionsByPriority).map(([priority, prioritySuggestions]) => {
          if (prioritySuggestions.length === 0) return null;
          
          return (
            <Collapse
              key={priority}
              defaultActiveKey={priority === AISuggestionPriority.CRITICAL ? [priority] : []}
              ghost
            >
              <Panel
                header={
                  <Space>
                    <Badge
                      count={prioritySuggestions.length}
                      color={PRIORITY_COLORS[priority as AISuggestionPriority]}
                    />
                    <span>{priority} 优先级</span>
                  </Space>
                }
                key={priority}
              >
                <List
                  dataSource={prioritySuggestions}
                  renderItem={renderSuggestionItem}
                  size="small"
                />
              </Panel>
            </Collapse>
          );
        })}
      </div>
    );
  }, [loading, filteredSuggestions, elements.length, suggestionsByPriority, renderSuggestionItem]);

  // 渲染设计评分
  const renderDesignScore = useCallback(() => (
    <Card size="small" className="design-score-card">
      <div className="score-header">
        <Title level={4}>设计评分</Title>
        <div className="score-value">
          <Progress
            type="circle"
            percent={overallScore}
            size={80}
            strokeColor={
              overallScore >= 80 ? '#52c41a' :
              overallScore >= 60 ? '#faad14' : '#ff4d4f'
            }
          />
        </div>
      </div>
      
      <div className="score-details">
        <Space direction="vertical" style={{ width: '100%' }}>
          <div className="score-item">
            <Text>待解决问题: </Text>
            <Badge count={filteredSuggestions.length} color="orange" />
          </div>
          <div className="score-item">
            <Text>已应用建议: </Text>
            <Badge count={appliedSuggestions.size} color="green" />
          </div>
          <div className="score-item">
            <Text>已忽略建议: </Text>
            <Badge count={ignoredSuggestions.size} color="gray" />
          </div>
        </Space>
      </div>
    </Card>
  ), [overallScore, filteredSuggestions.length, appliedSuggestions.size, ignoredSuggestions.size]);

  if (!visible) return null;

  return (
    <div className={`ai-design-assistant-panel ${className || ''}`}>
      <Card
        title={
          <Space>
            <RobotOutlined />
            <span>AI设计助手</span>
            <Switch
              checked={autoAnalyze}
              onChange={setAutoAnalyze}
              size="small"
            />
            <Text type="secondary" style={{ fontSize: 12 }}>
              自动分析
            </Text>
          </Space>
        }
        extra={
          <Space>
            <Tooltip title="手动分析">
              <Button
                type="text"
                size="small"
                icon={<ReloadOutlined />}
                loading={loading}
                onClick={analyzeDesign}
              />
            </Tooltip>
          </Space>
        }
        size="small"
      >
        <Tabs activeKey={activeTab} onChange={setActiveTab} size="small">
          <TabPane
            tab={
              <Space>
                <BulbOutlined />
                <span>建议</span>
                {filteredSuggestions.length > 0 && (
                  <Badge count={filteredSuggestions.length} size="small" />
                )}
              </Space>
            }
            key="suggestions"
          >
            {renderSuggestionsList()}
          </TabPane>
          
          <TabPane
            tab={
              <Space>
                <StarOutlined />
                <span>评分</span>
              </Space>
            }
            key="score"
          >
            {renderDesignScore()}
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default AIDesignAssistantPanel;
