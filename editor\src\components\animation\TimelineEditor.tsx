/**
 * TimelineEditor.tsx
 * 
 * 动画时间轴编辑器组件
 */

import React, { useState, useRef, useCallback, useEffect, useMemo } from 'react';
import {
  Button,
  Slider,
  InputNumber,
  Select,
  Tooltip,
  Dropdown,
  Menu,
  Space,
  Divider
} from 'antd';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  StepBackwardOutlined,
  StepForwardOutlined,
  PlusOutlined,
  DeleteOutlined,
  CopyOutlined,
  ScissorOutlined,
  ExpandOutlined,
  CompressOutlined
} from '@ant-design/icons';
import './TimelineEditor.module.css';

/**
 * 关键帧接口
 */
export interface Keyframe {
  id: string;
  time: number; // 时间（秒）
  value: any; // 关键帧值
  easing: string; // 缓动函数
  selected: boolean;
}

/**
 * 动画轨道接口
 */
export interface AnimationTrack {
  id: string;
  name: string;
  property: string; // 动画属性名
  componentId: string; // 关联的组件ID
  keyframes: Keyframe[];
  visible: boolean;
  locked: boolean;
  color: string;
}

/**
 * 动画配置接口
 */
export interface AnimationConfig {
  duration: number; // 总时长（秒）
  fps: number; // 帧率
  loop: boolean; // 是否循环
  autoplay: boolean; // 是否自动播放
}

/**
 * 缓动函数选项
 */
// const EASING_OPTIONS = [
//   { label: 'Linear', value: 'linear' },
//   { label: 'Ease', value: 'ease' },
//   { label: 'Ease In', value: 'ease-in' },
//   { label: 'Ease Out', value: 'ease-out' },
//   { label: 'Ease In Out', value: 'ease-in-out' },
//   { label: 'Cubic Bezier', value: 'cubic-bezier' }
// ];

/**
 * 时间轴编辑器属性
 */
export interface TimelineEditorProps {
  /** 动画轨道列表 */
  tracks: AnimationTrack[];
  /** 动画配置 */
  config: AnimationConfig;
  /** 当前播放时间 */
  currentTime: number;
  /** 是否正在播放 */
  isPlaying: boolean;
  /** 轨道变化回调 */
  onTracksChange?: (tracks: AnimationTrack[]) => void;
  /** 配置变化回调 */
  onConfigChange?: (config: AnimationConfig) => void;
  /** 播放控制回调 */
  onPlayStateChange?: (isPlaying: boolean) => void;
  /** 时间变化回调 */
  onTimeChange?: (time: number) => void;
  /** 关键帧选择回调 */
  onKeyframeSelect?: (keyframes: Keyframe[]) => void;
  /** 样式类名 */
  className?: string;
}

/**
 * 时间轴编辑器组件
 */
export const TimelineEditor: React.FC<TimelineEditorProps> = ({
  tracks,
  config,
  currentTime,
  isPlaying,
  onTracksChange,
  onConfigChange,
  onPlayStateChange,
  onTimeChange,
  onKeyframeSelect,
  className
}) => {
  const [selectedKeyframes, setSelectedKeyframes] = useState<Set<string>>(new Set());
  const [dragState, setDragState] = useState<{
    isDragging: boolean;
    dragType: 'keyframe' | 'playhead' | 'selection' | 'track';
    startTime: number;
    startX: number;
  } | null>(null);
  const [zoomLevel, setZoomLevel] = useState(1);
  const [scrollLeft, setScrollLeft] = useState(0);

  const timelineRef = useRef<HTMLDivElement>(null);
  const playheadRef = useRef<HTMLDivElement>(null);

  // 时间轴宽度和像素比例
  const timelineWidth = useMemo(() => {
    return config.duration * 100 * zoomLevel; // 每秒100像素 * 缩放级别
  }, [config.duration, zoomLevel]);

  const pixelsPerSecond = useMemo(() => {
    return 100 * zoomLevel;
  }, [zoomLevel]);

  // 时间转换为像素位置
  const timeToPixels = useCallback((time: number) => {
    return time * pixelsPerSecond;
  }, [pixelsPerSecond]);

  // 像素位置转换为时间
  const pixelsToTime = useCallback((pixels: number) => {
    return pixels / pixelsPerSecond;
  }, [pixelsPerSecond]);

  // 播放控制
  const handlePlay = useCallback(() => {
    onPlayStateChange?.(!isPlaying);
  }, [isPlaying, onPlayStateChange]);

  const handleStop = useCallback(() => {
    onPlayStateChange?.(false);
    onTimeChange?.(0);
  }, [onPlayStateChange, onTimeChange]);

  const handleStepBackward = useCallback(() => {
    const newTime = Math.max(0, currentTime - 1 / config.fps);
    onTimeChange?.(newTime);
  }, [currentTime, config.fps, onTimeChange]);

  const handleStepForward = useCallback(() => {
    const newTime = Math.min(config.duration, currentTime + 1 / config.fps);
    onTimeChange?.(newTime);
  }, [currentTime, config.duration, config.fps, onTimeChange]);

  // 添加关键帧
  const addKeyframe = useCallback((trackId: string, time: number) => {
    const track = tracks.find(t => t.id === trackId);
    if (!track) return;

    const newKeyframe: Keyframe = {
      id: `keyframe_${Date.now()}`,
      time,
      value: null, // 需要根据属性类型设置默认值
      easing: 'ease',
      selected: false
    };

    const updatedTracks = tracks.map(t => 
      t.id === trackId 
        ? { ...t, keyframes: [...t.keyframes, newKeyframe].sort((a, b) => a.time - b.time) }
        : t
    );

    onTracksChange?.(updatedTracks);
  }, [tracks, onTracksChange]);

  // 删除关键帧
  const deleteKeyframes = useCallback((keyframeIds: string[]) => {
    const updatedTracks = tracks.map(track => ({
      ...track,
      keyframes: track.keyframes.filter(kf => !keyframeIds.includes(kf.id))
    }));

    onTracksChange?.(updatedTracks);
    setSelectedKeyframes(new Set());
  }, [tracks, onTracksChange]);

  // 复制关键帧
  const copyKeyframes = useCallback(() => {
    const selectedKfs = tracks.flatMap(track => 
      track.keyframes.filter(kf => selectedKeyframes.has(kf.id))
    );
    
    if (selectedKfs.length > 0) {
      // 将关键帧数据存储到剪贴板
      const clipboardData = JSON.stringify(selectedKfs);
      navigator.clipboard.writeText(clipboardData);
    }
  }, [tracks, selectedKeyframes]);

  // 粘贴关键帧
  const pasteKeyframes = useCallback(async () => {
    try {
      const clipboardData = await navigator.clipboard.readText();
      const keyframes: Keyframe[] = JSON.parse(clipboardData);
      
      // 在当前时间位置粘贴关键帧
      const timeOffset = currentTime - (keyframes[0]?.time || 0);
      
      const updatedTracks = tracks.map(track => {
        const newKeyframes = keyframes
          .filter(kf => {
            // 根据属性匹配轨道
            return true; // 简化处理，实际需要更复杂的匹配逻辑
          })
          .map(kf => ({
            ...kf,
            id: `keyframe_${Date.now()}_${Math.random()}`,
            time: kf.time + timeOffset,
            selected: false
          }));
        
        return {
          ...track,
          keyframes: [...track.keyframes, ...newKeyframes].sort((a, b) => a.time - b.time)
        };
      });
      
      onTracksChange?.(updatedTracks);
    } catch (error) {
      console.error('粘贴关键帧失败:', error);
    }
  }, [tracks, currentTime, onTracksChange]);

  // 处理鼠标按下事件
  const handleMouseDown = useCallback((event: React.MouseEvent, type: 'keyframe' | 'playhead' | 'track' | 'selection', data?: any) => {
    event.preventDefault();
    
    const rect = timelineRef.current?.getBoundingClientRect();
    if (!rect) return;

    const startX = event.clientX - rect.left;
    const startTime = pixelsToTime(startX + scrollLeft);

    setDragState({
      isDragging: true,
      dragType: type,
      startTime,
      startX
    });

    if (type === 'playhead') {
      onTimeChange?.(Math.max(0, Math.min(config.duration, startTime)));
    } else if (type === 'track') {
      // 在轨道上添加关键帧
      addKeyframe(data.trackId, startTime);
    }
  }, [pixelsToTime, scrollLeft, config.duration, onTimeChange, addKeyframe]);

  // 处理鼠标移动事件
  const handleMouseMove = useCallback((event: MouseEvent) => {
    if (!dragState?.isDragging || !timelineRef.current) return;

    const rect = timelineRef.current.getBoundingClientRect();
    const currentX = event.clientX - rect.left;
    const currentTime = pixelsToTime(currentX + scrollLeft);

    if (dragState.dragType === 'playhead') {
      onTimeChange?.(Math.max(0, Math.min(config.duration, currentTime)));
    } else if (dragState.dragType === 'keyframe') {
      // 拖拽关键帧
      const timeDelta = currentTime - dragState.startTime;
      
      const updatedTracks = tracks.map(track => ({
        ...track,
        keyframes: track.keyframes.map(kf => 
          selectedKeyframes.has(kf.id)
            ? { ...kf, time: Math.max(0, Math.min(config.duration, kf.time + timeDelta)) }
            : kf
        ).sort((a, b) => a.time - b.time)
      }));
      
      onTracksChange?.(updatedTracks);
      setDragState({ ...dragState, startTime: currentTime });
    }
  }, [dragState, pixelsToTime, scrollLeft, config.duration, onTimeChange, tracks, selectedKeyframes, onTracksChange]);

  // 处理鼠标释放事件
  const handleMouseUp = useCallback(() => {
    setDragState(null);
  }, []);

  // 绑定全局鼠标事件
  useEffect(() => {
    if (dragState?.isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [dragState, handleMouseMove, handleMouseUp]);

  // 关键帧右键菜单
  const keyframeContextMenu = (
    <Menu>
      <Menu.Item key="copy" icon={<CopyOutlined />} onClick={copyKeyframes}>
        复制
      </Menu.Item>
      <Menu.Item key="paste" icon={<ScissorOutlined />} onClick={pasteKeyframes}>
        粘贴
      </Menu.Item>
      <Menu.Divider />
      <Menu.Item 
        key="delete" 
        icon={<DeleteOutlined />} 
        danger
        onClick={() => deleteKeyframes(Array.from(selectedKeyframes))}
      >
        删除
      </Menu.Item>
    </Menu>
  );

  return (
    <div className={`timeline-editor ${className || ''}`}>
      {/* 工具栏 */}
      <div className="timeline-toolbar">
        <Space>
          {/* 播放控制 */}
          <Button.Group>
            <Button icon={<StepBackwardOutlined />} onClick={handleStepBackward} />
            <Button 
              icon={isPlaying ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
              onClick={handlePlay}
              type={isPlaying ? 'primary' : 'default'}
            />
            <Button icon={<StepForwardOutlined />} onClick={handleStepForward} />
          </Button.Group>

          <Divider type="vertical" />

          {/* 时间显示 */}
          <Space>
            <span>时间:</span>
            <InputNumber
              value={Number(currentTime.toFixed(2))}
              min={0}
              max={config.duration}
              step={0.01}
              size="small"
              style={{ width: 80 }}
              onChange={(value) => onTimeChange?.(value || 0)}
            />
            <span>/ {config.duration}s</span>
          </Space>

          <Divider type="vertical" />

          {/* 缩放控制 */}
          <Space>
            <span>缩放:</span>
            <Slider
              value={zoomLevel}
              min={0.1}
              max={5}
              step={0.1}
              style={{ width: 100 }}
              onChange={setZoomLevel}
            />
            <Button 
              size="small" 
              icon={<ExpandOutlined />}
              onClick={() => setZoomLevel(Math.min(5, zoomLevel * 1.5))}
            />
            <Button 
              size="small" 
              icon={<CompressOutlined />}
              onClick={() => setZoomLevel(Math.max(0.1, zoomLevel / 1.5))}
            />
          </Space>

          <Divider type="vertical" />

          {/* 配置 */}
          <Space>
            <span>时长:</span>
            <InputNumber
              value={config.duration}
              min={1}
              max={300}
              size="small"
              style={{ width: 80 }}
              onChange={(value) => onConfigChange?.({ ...config, duration: value || 1 })}
            />
            <span>FPS:</span>
            <Select
              value={config.fps}
              size="small"
              style={{ width: 80 }}
              onChange={(value) => onConfigChange?.({ ...config, fps: value })}
            >
              <Select.Option value={24}>24</Select.Option>
              <Select.Option value={30}>30</Select.Option>
              <Select.Option value={60}>60</Select.Option>
            </Select>
          </Space>
        </Space>
      </div>

      {/* 时间轴区域 */}
      <div className="timeline-container">
        {/* 轨道列表 */}
        <div className="track-list">
          {tracks.map(track => (
            <div key={track.id} className="track-header">
              <div className="track-name">{track.name}</div>
              <div className="track-controls">
                <Button type="text" size="small" icon={<PlusOutlined />} />
              </div>
            </div>
          ))}
        </div>

        {/* 时间轴内容 */}
        <div 
          ref={timelineRef}
          className="timeline-content"
          style={{ width: timelineWidth }}
          onMouseDown={(e) => handleMouseDown(e, 'playhead')}
        >
          {/* 时间刻度 */}
          <div className="time-ruler">
            {Array.from({ length: Math.ceil(config.duration) + 1 }, (_, i) => (
              <div
                key={i}
                className="time-mark"
                style={{ left: timeToPixels(i) }}
              >
                <span>{i}s</span>
              </div>
            ))}
          </div>

          {/* 播放头 */}
          <div
            ref={playheadRef}
            className="playhead"
            style={{ left: timeToPixels(currentTime) }}
          />

          {/* 轨道内容 */}
          <div className="tracks-content">
            {tracks.map(track => (
              <div
                key={track.id}
                className="track-content"
                onMouseDown={(e) => handleMouseDown(e, 'track', { trackId: track.id })}
              >
                {/* 关键帧 */}
                {track.keyframes.map(keyframe => (
                  <Dropdown
                    key={keyframe.id}
                    overlay={keyframeContextMenu}
                    trigger={['contextMenu']}
                  >
                    <div
                      className={`keyframe ${selectedKeyframes.has(keyframe.id) ? 'selected' : ''}`}
                      style={{
                        left: timeToPixels(keyframe.time),
                        backgroundColor: track.color
                      }}
                      onClick={(e) => {
                        e.stopPropagation();
                        const newSelection = new Set(selectedKeyframes);
                        if (e.ctrlKey || e.metaKey) {
                          if (newSelection.has(keyframe.id)) {
                            newSelection.delete(keyframe.id);
                          } else {
                            newSelection.add(keyframe.id);
                          }
                        } else {
                          newSelection.clear();
                          newSelection.add(keyframe.id);
                        }
                        setSelectedKeyframes(newSelection);
                        
                        const selectedKfs = tracks.flatMap(t => 
                          t.keyframes.filter(kf => newSelection.has(kf.id))
                        );
                        onKeyframeSelect?.(selectedKfs);
                      }}
                      onMouseDown={(e) => {
                        e.stopPropagation();
                        handleMouseDown(e, 'keyframe');
                      }}
                    >
                      <Tooltip title={`${keyframe.time.toFixed(2)}s - ${keyframe.easing}`}>
                        <div className="keyframe-diamond" />
                      </Tooltip>
                    </div>
                  </Dropdown>
                ))}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TimelineEditor;
